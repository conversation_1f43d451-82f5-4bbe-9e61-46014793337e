# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/28
# __author:       <PERSON><PERSON><PERSON> Shi
"""
Composite Experiment abstract base class.
"""

import asyncio
import copy
from datetime import datetime
from typing import TYPE_CHECKING, Dict, List, Optional, Union

import numpy as np

from ..analysis.algorithms import IQdiscriminator
from ..analysis.standard_curve_analysis import StandardCurveAnalysis
from ..analysis.top_analysis import TopAnalysis, generate_experiment_data
from ..concurrent import ConcurrentCR
from ..concurrent.util import is_force_stop
from ..concurrent.worker import run_analysis_process
from ..errors import (
    AnalysisOptionsError,
    ExperimentOptionsError,
    CompositeExperimentError,
    ChildExperimentExecutionError,
)
from ..experiments.base_experiment import BaseExperiment, BaseQubitsType
from ..experiments.top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..instrument import Instrument
from ..log import pyqlog
from ..pulse import PulseCorrection
from ..qubit import Coupler, Qubit, QubitPair
from ..structures import Options
from ..tools.utilities import format_results, judge_exp_failed
from ..types import ExperimentRunMode, ExperimentType, CompositeExperimentTolerance
from ._wrappers import coroutine_experiment_status_decorator

if TYPE_CHECKING:
    from pyQCat.config import PyqcatConfig


class AsyncEnumerate:
    def __init__(self, seq: Union[List, np.ndarray]):
        self._seq = seq
        self._counts = len(seq)
        self._index = 0

    def __aiter__(self):
        return self

    async def __anext__(self):
        if self._index >= self._counts:
            raise StopAsyncIteration

        self._index += 1

        await asyncio.sleep(0)

        return self._index - 1, self._seq[self._index - 1]


class CompositeExperiment(BaseExperiment):
    """Composite Experiment base class"""

    _sub_experiment_class = TopExperiment
    EXP_TYPE = ExperimentType.COMP

    def __init__(
        self,
        inst: Instrument,
        qubits: Union[Qubit, List[Qubit]],
        couplers: Union[Coupler, List[Coupler]] = None,
        qubit_pair: QubitPair = None,
        compensates: Dict[BaseQubitsType, PulseCorrection] = None,
        discriminators: Union[IQdiscriminator, List[IQdiscriminator]] = None,
        working_dc: Dict = None,
        ac_bias: Dict = None,
        is_paternal: bool = True,
        config: "PyqcatConfig" = None,
    ):
        self._experiments = []

        # 2024/09/14: Minimize experimental metadata, used for compressing composite experimental memory
        self._minimize_experiments = []

        # 子实验异常记录
        self._child_exceptions = []

        # base experiment initialize.
        super().__init__(
            inst,
            qubits,
            couplers,
            qubit_pair,
            compensates,
            discriminators,
            working_dc,
            ac_bias,
            is_paternal,
            config,
        )

        # Child experiment works like a hook.
        # The experiment :meth:`run` method will deepcopy child experiment and
        # user can call `self.child_experiment.set_analysis_options` after
        # `__init__` method called.
        self._child_experiment = self._create_child_experiment()

        self._require_id_set = set()

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("child_exp_options", dict)
        options.set_validator(
            "run_mode", [ExperimentRunMode.sync_mode, ExperimentRunMode.async_mode]
        )
        # options.set_validator("async_time_out", float)
        options.set_validator("quality_filter", bool)
        options.set_validator("is_sub_merge", bool)
        options.child_exp_options = (
            cls._sub_experiment_class._default_experiment_options()
        )
        options.run_mode = ExperimentRunMode.sync_mode
        # options.async_time_out = 10
        options.quality_filter = False
        options.is_sub_merge = True

        # Do you want to enable the minimize experiment mode
        options.set_validator("minimize_mode", bool)
        options.minimize_mode = False

        # 复合实验容错类型配置
        options.set_validator(
            "tolerance_type",
            [
                CompositeExperimentTolerance.TOLERANT,
                CompositeExperimentTolerance.INTOLERANT,
            ],
        )
        options.tolerance_type = CompositeExperimentTolerance.TOLERANT

        # 异常详细程度配置
        options.set_validator("exception_detail_level", ["basic", "detailed"])
        options.exception_detail_level = "detailed"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("child_ana_options", dict)
        options.child_ana_options = (
            cls._sub_experiment_class._default_analysis_options()
        )

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.x_data = None
        options.support_context = cls._sub_experiment_class._default_run_options().get(
            "support_context"
        )
        options.link_child_exp = False

        return options

    @property
    def child_experiment(self):
        """Get child experiment."""
        return self._child_experiment

    def _check_options(self):
        super()._check_options()

        self.experiment_options.child_exp_options.record_text = (
            self.experiment_options.record_text
        )
        self._child_experiment.set_experiment_options(
            **self.experiment_options.child_exp_options
        )
        self._child_experiment.set_analysis_options(
            **self.analysis_options.child_ana_options
        )

        self.child_experiment.run_options.parallel_token = (
            self.run_options.parallel_token
        )
        self.child_experiment.run_options.parallel = self.run_options.parallel

        # global run mode setting
        try:
            global_run_mode = self.config.get_run("run_mode")
            if (
                global_run_mode == ExperimentRunMode.async_mode
                and global_run_mode
                in self.experiment_options.validate_options.run_mode[0]
            ):
                self.experiment_options.run_mode = ExperimentRunMode.async_mode
            elif global_run_mode == ExperimentRunMode.sync_mode:
                self.experiment_options.run_mode = ExperimentRunMode.sync_mode
        except Exception:
            pass

    def run(self):
        """Run composite experiment."""
        self._check_options()
        self._validate_options()
        self._config_file_sys()

    def _run_analysis(
        self, x_data: Union[List, np.ndarray], analysis_class: type[TopAnalysis]
    ):
        """Run composite analysis.

        Args:
            x_data (np.ndarray): The value of the x data.
            analysis_class (type(TopAnalysis)): analysis class.
        """
        # create experiment data
        experiment_data = self._create_composite_experiment_data(x_data)

        # analysis args
        args = (analysis_class, experiment_data, self._analysis_options, self.file)
        self._analysis = run_analysis_process(*args)

        # log result
        self._log_result()

    def _log_result(self):
        if (
            # self.experiment_options.show_result
            not self.run_options.parallel
            and self.analysis
        ):
            pyqlog.log("RESULT", format_results(self.analysis))

    def _create_composite_experiment_data(self, x_data):
        # filer error child experiment
        x_data = list(x_data)

        # Choose whether to extract the minimum experimental information
        if self.experiment_options.minimize_mode is True:
            x_data, sub_analysis_list = self._extract_mini_child_analysis_data(x_data)
        else:
            x_data, sub_analysis_list = self._extract_child_analysis_data(x_data)

        # create experiment data
        # fix: kangKang Geng
        # Previously, if a parent experiment lacked a task_id,
        # its exp_id would mistakenly be written as the task_id in record.txt.
        experiment_data = generate_experiment_data(
            x_data, sub_analysis_list, self.record_id, self._metadata()
        )
        if self.run_options.x_data2:
            experiment_data._x_data2 = self.run_options.x_data2

        # record analysis options in meta process meta
        experiment_data.metadata.process_meta["analysis_options"] = (
            self.analysis_options
        )

        # feat: kangKang Geng
        # Add record_id to metadata for subsequent process access
        experiment_data.metadata.process_meta["record_id"] = self.record_id

        return experiment_data

    def _extract_child_analysis_data(self, x_data: List):
        """Interface before optimization, obtain analytical information
        from sub experimental classes.

        Args:
            x_data (List): Scan data

        Returns:
            List: Analysis List
        """
        done_indexes = [
            i for i, sub_exp in enumerate(self._experiments) if sub_exp.status.is_done()
        ]

        if self.experiment_options.quality_filter is True:
            done_indexes = [
                idx
                for idx in done_indexes
                if not judge_exp_failed(self._experiments[idx].analysis.quality)
            ]

        x_data = [x_data[idx] for idx in done_indexes]
        done_experiments = [self._experiments[idx] for idx in done_indexes]
        return x_data, [exp.analysis for exp in done_experiments]

    def _extract_mini_child_analysis_data(self, x_data: List):
        """Optimized interface to extract key information from the smallest analysis unit.

        Args:
            x_data (List): Scan data

        Returns:
            List: Minimum analysis unit
        """
        done_indexes = []
        for idx, child_ana in enumerate(self._minimize_experiments):
            if child_ana and (
                self.experiment_options.quality_filter is False
                or (child_ana.quality and not judge_exp_failed(child_ana.quality))
            ):
                done_indexes.append(idx)
        x_data = [x_data[idx] for idx in done_indexes]
        return x_data, [self._minimize_experiments[idx] for idx in done_indexes]

    def set_child_exp_options(self, **fields):
        """Set the child experiment options.

        Args:
            fields: The fields to update the experiment options

        Raises:
            ExperimentOptionsError: If the field passed in is not a supported options
        """
        for field in fields:
            if field not in self._experiment_options.child_exp_options:
                raise ExperimentOptionsError(
                    f"{self._label}({self._child_experiment.label})",
                    key=field,
                    value=fields.get(field),
                    msg=f"field {field} option must be defined in advance!",
                )
        self._experiment_options.child_exp_options.update(**fields)

    def set_child_ana_options(self, **fields):
        """Set the child experiment options.

        Args:
            fields: The fields to update the analysis options

        Raises:
            AnalysisOptionsError: If the field passed in is not a supported options
        """
        for field in fields:
            if field not in self._analysis_options.child_ana_options:
                raise AnalysisOptionsError(
                    f"{self._label}({self._child_experiment.label})",
                    key=field,
                    value=fields.get(field),
                    msg=f"field {field} option must be defined in advance!",
                )
        self._analysis_options.child_ana_options.update(**fields)

    def _create_child_experiment(self) -> Union[BaseExperiment, TopExperiment]:
        """Create a child experiment object.

        Returns:
            BaseExperiment object.
        """
        child_exp = self._sub_experiment_class(
            inst=self.inst,
            qubits=self.qubits,
            couplers=self.couplers,
            qubit_pair=self.qubit_pairs,
            compensates=self.compensates,
            discriminators=self.discriminator,
            working_dc=self.working_dc,
            ac_bias=self.ac_bias,
            is_paternal=False,
            config=self.config,
        )
        return child_exp

    def component_experiment(
        self, index=None
    ) -> Union[BaseExperiment, List[BaseExperiment]]:
        """Return the component Experiment object.

        Args:
            index (int): Experiment index, or ``None`` if all experiments are to be returned.
        Returns:
            BaseExperiment: The component experiment(s).
        """
        if index is None:
            return self._experiments
        return self._experiments[index]

    def sync_child_require_id(self, exp: Optional["BaseExperiment"] = None):
        experiments = self._experiments if exp is None else [exp]
        for experiment in experiments:
            if experiment.EXP_TYPE == ExperimentType.TOP:
                self._require_id_set.add(experiment.require_id)
            elif experiment.EXP_TYPE == ExperimentType.COMP:
                self._require_id_set = self._require_id_set | experiment._require_id_set

    def _check_simulator_data(self, exp, index, **kwargs):
        simulator_data_path = self.run_options.simulator_data_path
        if simulator_data_path:
            exp.set_run_options(simulator_data_path=simulator_data_path[index])
            exp.set_experiment_options(simulator_data_path=simulator_data_path[index])
        exp.set_run_options(simulator_index=index)
        exp.set_experiment_options(use_simulator=self.experiment_options.use_simulator)
        # Multilayer composite experimental addressing
        if not exp.run_options.simulator_name:
            if self.run_options.simulator_name:
                exp.set_run_options(simulator_name=self.run_options.simulator_name)
            else:
                exp.set_run_options(simulator_name=self._label)

    def _set_result_path(self):
        pass

    def _alone_save_result(self):
        """Alone save some special result."""
        pass

    def _setup_child_experiment(self, exp: "TopExperiment", index: int, value: float):
        # some run options set here.
        pass

    def _handle_child_result(self, exp: "TopExperiment"):
        # collect child experiment result and provide it for parent.
        pass

    def _handle_child_experiment(self, exp: "TopExperiment"):
        """Hook function after sub experiment execution.

        Args:
            exp (TopExperiment): Child experiment
        """
        self._handle_child_result(exp)

        # Add minimum analysis unit
        if self.experiment_options.minimize_mode is True and exp.analysis:
            self._minimize_experiments[exp.run_options.index] = (
                exp.analysis.analysis_program()
            )

    def _add_child_experiment(self, exp: "BaseExperiment"):
        """Add sub experiment information to the current experiment.

        Args:
            exp (TopExperiment): Child experiment
        """
        self.sync_child_require_id(exp)
        if self.experiment_options.minimize_mode is False:
            self._experiments.append(exp)
        else:
            self._minimize_experiments.append(None)

    async def _async_composite_run_base(self):
        tasks = []
        child_exps = []
        child_info = []  # 存储子实验信息用于异常处理

        async for i, x in AsyncEnumerate(self.run_options.x_data):

            # feat: Check whether the user has entered forced termination information.
            # If it does, the process of creating asynchronous tasks in a loop will be
            # skipped. Otherwise, if there are too many asynchronous tasks, it will
            # cause the stop process to be slow
            if is_force_stop():
                break

            child_exp: "TopExperiment" = copy.deepcopy(self._child_experiment)
            child_exps.append(child_exp)
            child_info.append((int(i), x, child_exp))  # 记录子实验信息

            self._setup_child_experiment(child_exp, int(i), x)
            self._add_child_experiment(child_exp)

            # 创建带异常处理的任务
            task = asyncio.create_task(
                self._run_child_experiment_with_exception_handling(child_exp, int(i), x)
            )
            tasks.append(task)

        # 等待所有任务完成，使用return_when=ALL_COMPLETED来处理异常
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except asyncio.CancelledError:
                pyqlog.error(f"Async run experiment {self} cancelled.")

        # 检查子实验完成状态
        any_child_done = any(
            child_exp.check_exp_is_done(self.run_options.link_child_exp)
            for child_exp in child_exps
        )
        if not any_child_done and child_exps:
            child_exps[0].check_exp_is_done(open=True)

        # 记录最终异常摘要
        self._log_final_exception_summary()
        await self._async_run_analysis()

    async def _run_child_experiment_with_exception_handling(
        self, child_exp: "TopExperiment", child_index: int, child_x_value: float
    ):
        """带异常处理的子实验运行方法"""
        try:
            await child_exp.run_experiment(
                clear=True,
                callback=self._handle_child_experiment,
            )
        except Exception as e:
            # 处理子实验异常
            self._handle_child_experiment_exception(
                child_index, child_x_value, child_exp, e
            )
            # 如果是不容忍类型，异常已经在_handle_child_experiment_exception中抛出
            # 如果是容忍类型，异常被记录但不会传播

    async def _sync_composite_run(self):
        for i, x in enumerate(self.run_options.x_data):
            if is_force_stop():
                break
            child_exp: "TopExperiment" = copy.deepcopy(self._child_experiment)
            self._setup_child_experiment(child_exp, i, x)

            try:
                await child_exp.run_experiment()
                child_exp.check_exp_is_done(self.run_options.link_child_exp)
                self._add_child_experiment(child_exp)
                self._handle_child_experiment(child_exp)
            except Exception as e:
                # 处理子实验异常
                self._handle_child_experiment_exception(i, x, child_exp, e)
                # 如果是不容忍类型，异常已经在_handle_child_experiment_exception中抛出
                # 如果是容忍类型，继续执行下一个子实验

        # 记录最终异常摘要
        self._log_final_exception_summary()
        await self._async_run_analysis()

    async def _async_run_analysis(self):
        # x data and analysis cls
        x_data = self.run_options.x_data
        analysis_class = self.run_options.analysis_class

        # create experiment data
        experiment_data = self._create_composite_experiment_data(x_data)

        # analysis args
        if analysis_class:
            args = (analysis_class, experiment_data, self._analysis_options, self.file)
            if self.run_options.parallel:
                # ConcurrentCR accelerate analysis processes
                result = await ConcurrentCR().run_concurrent_job(
                    run_analysis_process, *args
                )
                if isinstance(result, Exception):
                    raise result
                self._analysis = result
            else:
                self._analysis = run_analysis_process(*args)

            # log result
            self._log_result()
        else:
            self._analysis = StandardCurveAnalysis.empty_analysis()

    @coroutine_experiment_status_decorator
    async def run_experiment(self, **kwargs):
        """Run composite experiment."""
        self.run()

        if self.experiment_options.run_mode == ExperimentRunMode.sync_mode:
            await self._sync_composite_run()
        elif self.experiment_options.run_mode == ExperimentRunMode.async_mode:
            await self._async_composite_run_base()

        # set result path
        self._set_result_path()

        # alone save result
        self._alone_save_result()

        # do sth after analysis.
        if kwargs.get("callback") is not None:
            kwargs.get("callback")(self)

        # collect require id
        self.sync_child_require_id()

    def _handle_child_experiment_exception(
        self,
        child_index: int,
        child_x_value: float,
        child_exp: "TopExperiment",
        exception: Exception,
    ):
        """处理子实验异常

        Args:
            child_index: 子实验索引
            child_x_value: 子实验的x参数值
            child_exp: 子实验对象
            exception: 原始异常
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        child_exception = ChildExperimentExecutionError(
            child_index=child_index,
            child_x_value=child_x_value,
            child_exp_name=child_exp.__class__.__name__,
            original_exception=exception,
            timestamp=timestamp,
        )

        # 记录异常
        self._child_exceptions.append(child_exception)

        # 根据异常详细程度记录日志
        if self.experiment_options.exception_detail_level == "detailed":
            pyqlog.error(f"子实验执行异常详情: {child_exception}")
            if hasattr(exception, "__traceback__"):
                import traceback

                pyqlog.debug(
                    f"异常堆栈: {''.join(traceback.format_tb(exception.__traceback__))}"
                )
        else:
            pyqlog.error(
                f"子实验异常: 索引{child_index}, 参数{child_x_value}, "
                f"异常类型{type(exception).__name__}"
            )

        # 根据容错类型决定处理策略
        if (
            self.experiment_options.tolerance_type
            == CompositeExperimentTolerance.INTOLERANT
        ):
            # 不容忍类型：立即抛出复合实验异常
            raise CompositeExperimentError(
                composite_exp_name=self.__class__.__name__,
                msg=f"子实验执行失败，索引: {child_index}, 参数值: {child_x_value}",
                child_exceptions=[child_exception],
            )
        # 容忍类型：仅记录异常，继续执行

    def _get_exception_summary(self) -> str:
        """获取异常摘要信息"""
        if not self._child_exceptions:
            return "无子实验异常"

        summary = f"共发生 {len(self._child_exceptions)} 个子实验异常:\n"
        for i, exc in enumerate(self._child_exceptions, 1):
            summary += f"  {i}. 索引{exc.child_index}: {type(exc.original_exception).__name__}\n"
        return summary

    def _log_final_exception_summary(self):
        """记录最终的异常摘要"""
        if self._child_exceptions:
            summary = self._get_exception_summary()
            pyqlog.warning(f"复合实验 {self.__class__.__name__} 异常摘要:\n{summary}")

            # 如果是容忍类型且有异常，给出提示
            if (
                self.experiment_options.tolerance_type
                == CompositeExperimentTolerance.TOLERANT
            ):
                pyqlog.info(f"复合实验采用容忍模式，已跳过失败的子实验，继续执行分析")
